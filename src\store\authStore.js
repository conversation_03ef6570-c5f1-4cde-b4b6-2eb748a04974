import { create } from 'zustand';
import axios from 'axios';
import { AUTH_ENDPOINTS } from '../lib/constants';

// Create axios instance with interceptors
const axiosInstance = axios.create({
    withCredentials: true,
});

// Add request interceptor to include token in headers
axiosInstance.interceptors.request.use(
    (config) => {
        const token = localStorage.getItem('token');
        if (token) {
            config.headers.Authorization = `Bear<PERSON> ${token}`;
        }
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

// Add response interceptor to handle token expiration
axiosInstance.interceptors.response.use(
    (response) => response,
    (error) => {
        if (error.response?.status === 401) {
            // Token expired or invalid
            localStorage.removeItem('token');
            window.location.href = '/login';
        }
        return Promise.reject(error);
    }
);

const useAuthStore = create((set, get) => ({
    user: null,
    loading: false,
    error: null,
    isAuthenticated: false,

    // INITIALIZE - Check if user is logged in on app start
    initialize: async () => {
        const token = localStorage.getItem('token');
        if (token) {
            try {
                const { data } = await axiosInstance.get(AUTH_ENDPOINTS.CURRENT_USER);
                set({ user: data?.user, isAuthenticated: true });
                return data?.user;
            } catch (error) {
                localStorage.removeItem('token');
                set({ user: null, isAuthenticated: false });
            }
        }
        return null;
    },

    // REGISTER
    register: async (userData) => {
        set({ loading: true, error: null });
        try {
            const { data } = await axiosInstance.post(AUTH_ENDPOINTS.REGISTER, userData);
            return data;
        } catch (err) {
            const errorMsg = err?.response?.data?.error || 'Registration failed';
            set({ error: errorMsg });
            return null;
        } finally {
            set({ loading: false });
        }
    },

    // VERIFY OTP
    verifyOtp: async (otpData) => {
        set({ loading: true, error: null });
        try {
            const { data } = await axiosInstance.post(AUTH_ENDPOINTS.VERIFY_OTP, otpData);
            if (data.token) {
                localStorage.setItem('token', data.token);
                set({ user: data.user, isAuthenticated: true });
            }
            return data;
        } catch (err) {
            const errorMsg = err?.response?.data?.error || 'OTP verification failed';
            set({ error: errorMsg });
            return null;
        } finally {
            set({ loading: false });
        }
    },

    // RESEND OTP
    resendOtp: async (email) => {
        set({ loading: true, error: null });
        try {
            const { data } = await axiosInstance.post(AUTH_ENDPOINTS.RESEND_OTP, { email });
            return data;
        } catch (err) {
            const errorMsg = err?.response?.data?.error || 'Failed to resend OTP';
            set({ error: errorMsg });
            return null;
        } finally {
            set({ loading: false });
        }
    },

    // LOGIN
    login: async (credentials) => {
        set({ loading: true, error: null });
        try {
            const { data } = await axiosInstance.post(AUTH_ENDPOINTS.LOGIN, credentials);
            if (data.token) {
                localStorage.setItem('token', data.token);
                set({ user: data.user, isAuthenticated: true });
            }
            return data;
        } catch (err) {
            const errorMsg = err?.response?.data?.error || 'Login failed';
            set({ error: errorMsg });
            return null;
        } finally {
            set({ loading: false });
        }
    },

    // GET CURRENT USER
    fetchCurrentUser: async () => {
        set({ loading: true, error: null });
        try {
            const { data } = await axiosInstance.get(AUTH_ENDPOINTS.CURRENT_USER);
            set({ user: data?.user, isAuthenticated: true });
            return data?.user;
        } catch (err) {
            const errorMsg = err?.response?.data?.error || 'User not authenticated';
            set({ error: errorMsg, user: null, isAuthenticated: false });
            localStorage.removeItem('token');
            return null;
        } finally {
            set({ loading: false });
        }
    },

    // LOGOUT
    logout: async () => {
        set({ loading: true, error: null });
        try {
            await axiosInstance.post(AUTH_ENDPOINTS.LOGOUT);
        } catch (err) {
            console.error('Logout error:', err);
        } finally {
            localStorage.removeItem('token');
            set({ user: null, isAuthenticated: false, loading: false });
        }
    },

    // RESET ERROR
    clearError: () => set({ error: null }),
}));

export default useAuthStore;