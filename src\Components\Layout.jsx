import React from "react";
import { Outlet, NavLink, useLocation } from "react-router-dom";
import Profile from "./company/Profile";

const Layout = () => {
  const location = useLocation();
  const hideSidebar = location.pathname === "/aptitude" || location.pathname === "/job-create" || location.pathname === "/interview";
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Top Bar */}
      <header className="fixed top-0 left-0 w-full z-50 shadow h-16 flex items-center border-b border-gray-200" style={{ background: 'rgb(35, 65, 75)' }}>
        <h1 className="text-2xl font-bold text-white ml-8">Proyuj</h1>
      </header>
      <div className="flex flex-1 pt-16">
        {/* Sidebar */}
        {!hideSidebar && (
          <aside className="w-64 min-h-screen bg-gradient-to-b from-[rgb(35,65,75)] to-gray-900 border-r border-gray-900 shadow-2xl flex flex-col p-0">
            <div className="px-8 py-7 border-b border-gray-800 text-2xl font-extrabold text-white tracking-tight flex items-center gap-3">
              <svg className="w-7 h-7 text-white opacity-80" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10" /><path d="M12 8v4l3 3" /></svg>
              Menu
            </div>
            <nav className="flex flex-col gap-4 px-4 py-8 custom-scrollbar">
              <NavLink to="/dashboard" className={({isActive}) => isActive ? "flex items-center gap-3 text-xl font-bold text-white bg-white/10 shadow-lg scale-[1.03] rounded-full px-8 py-4 transition-all duration-200" : "flex items-center gap-3 text-xl font-medium text-gray-200 hover:bg-white/5 hover:text-white rounded-full px-8 py-4 transition-all duration-200"}>
                <span className="text-xl">🏢</span> Company Dashboard
              </NavLink>
              <NavLink to="/job-create" className={({isActive}) => isActive ? "flex items-center gap-3 text-lg font-bold text-white bg-white/10 shadow-lg ring-2 ring-blue-400/40 scale-[1.03] rounded-full px-6 py-3 transition-all duration-200" : "flex items-center gap-3 text-lg font-medium text-gray-200 hover:bg-white/5 hover:text-white rounded-full px-6 py-3 transition-all duration-200"}>
                <span className="text-xl">➕</span> Create job
              </NavLink>
              <NavLink to="/aptitude" className={({isActive}) => isActive ? "flex items-center gap-3 text-lg font-bold text-white bg-white/10 shadow-lg ring-2 ring-blue-400/40 scale-[1.03] rounded-full px-6 py-3 transition-all duration-200" : "flex items-center gap-3 text-lg font-medium text-gray-200 hover:bg-white/5 hover:text-white rounded-full px-6 py-3 transition-all duration-200"}>
                <span className="text-xl">📝</span> Aptitude
              </NavLink>
              <a
                href="https://interviewpage-inky.vercel.app/"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-3 text-lg font-medium text-gray-200 hover:bg-white/5 hover:text-white rounded-full px-6 py-3 transition-all duration-200"
              >
                <span className="text-xl">🎤</span> Interview
              </a>
              <NavLink to="/profile" className={({isActive}) => isActive ? "flex items-center gap-3 text-lg font-bold text-white bg-white/10 shadow-lg ring-2 ring-green-400/40 scale-[1.03] rounded-full px-6 py-3 transition-all duration-200" : "flex items-center gap-3 text-lg font-medium text-gray-200 hover:bg-white/5 hover:text-white rounded-full px-6 py-3 transition-all duration-200"}>
                <span className="text-xl">👤</span> Profile
              </NavLink>
            </nav>
          </aside>
        )}
        {/* Main Content */}
        <main className="flex-1 p-10 flex flex-col items-center justify-start">
          <Outlet />
        </main>
      </div>
    </div>
  );
};

export default Layout;
