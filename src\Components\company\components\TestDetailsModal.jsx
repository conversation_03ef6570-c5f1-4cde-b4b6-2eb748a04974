import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  XMarkIcon,
  ClockIcon,
  DocumentTextIcon,
  UserGroupIcon,
  CalendarIcon,
  CheckCircleIcon,
  XCircleIcon,
  BriefcaseIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';

const TestDetailsModal = ({ isOpen, onClose, test }) => {
  if (!isOpen || !test) return null;

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getTestStatus = (test) => {
    const now = new Date();
    const startDate = new Date(test.scheduledDate);
    const endDate = new Date(test.endDate);

    if (!test.isActive) return { status: 'inactive', color: 'gray', text: 'Inactive' };
    if (now < startDate) return { status: 'scheduled', color: 'blue', text: 'Scheduled' };
    if (now >= startDate && now <= endDate) return { status: 'active', color: 'green', text: 'Active' };
    if (now > endDate) return { status: 'completed', color: 'purple', text: 'Completed' };
    
    return { status: 'unknown', color: 'gray', text: 'Unknown' };
  };

  const status = getTestStatus(test);
  const participantCount = test.participants?.length || 0;
  const completedCount = test.participants?.filter(p => p.status === 'completed').length || 0;
  const inProgressCount = test.participants?.filter(p => p.status === 'in-progress').length || 0;
  const assignedCount = test.participants?.filter(p => p.status === 'assigned').length || 0;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          className="bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden"
        >
          {/* Header */}
          <div className="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold mb-2">{test.testName}</h2>
                <div className="flex items-center gap-4">
                  <span className={`px-3 py-1 text-sm font-medium rounded-full bg-${status.color}-100 text-${status.color}-800`}>
                    {status.text}
                  </span>
                  <span className="text-blue-100">
                    Created: {formatDate(test.createdAt)}
                  </span>
                </div>
              </div>
              <button
                onClick={onClose}
                className="p-2 hover:bg-white/20 rounded-lg transition-colors"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="p-6 overflow-y-auto max-h-[70vh]">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Basic Information */}
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-800 mb-4">Test Information</h3>
                  
                  {test.description && (
                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
                      <p className="text-gray-600 bg-gray-50 p-3 rounded-lg">{test.description}</p>
                    </div>
                  )}

                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <ClockIcon className="h-5 w-5 text-gray-600" />
                        <span className="font-medium text-gray-800">Duration</span>
                      </div>
                      <span className="text-2xl font-bold text-blue-600">{test.duration}</span>
                      <span className="text-gray-600 ml-1">minutes</span>
                    </div>

                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <ChartBarIcon className="h-5 w-5 text-gray-600" />
                        <span className="font-medium text-gray-800">Passing Score</span>
                      </div>
                      <span className="text-2xl font-bold text-green-600">{test.passingScore}</span>
                      <span className="text-gray-600 ml-1">%</span>
                    </div>

                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <DocumentTextIcon className="h-5 w-5 text-gray-600" />
                        <span className="font-medium text-gray-800">Questions</span>
                      </div>
                      <span className="text-2xl font-bold text-purple-600">{test.questions?.length || 0}</span>
                    </div>

                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <span className="font-medium text-gray-800">Total Points</span>
                      </div>
                      <span className="text-2xl font-bold text-orange-600">{test.totalPoints || 0}</span>
                    </div>
                  </div>
                </div>

                {/* Schedule */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-800 mb-4">Schedule</h3>
                  <div className="space-y-3">
                    <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                      <CalendarIcon className="h-5 w-5 text-blue-600" />
                      <div>
                        <div className="font-medium text-gray-800">Start Date</div>
                        <div className="text-blue-600">{formatDate(test.scheduledDate)}</div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-3 p-3 bg-red-50 rounded-lg">
                      <CalendarIcon className="h-5 w-5 text-red-600" />
                      <div>
                        <div className="font-medium text-gray-800">End Date</div>
                        <div className="text-red-600">{formatDate(test.endDate)}</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Test Settings */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-800 mb-4">Settings</h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <span className="font-medium text-gray-800">Allowed Attempts</span>
                      <span className="text-blue-600 font-semibold">{test.allowedAttempts}</span>
                    </div>
                    
                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <span className="font-medium text-gray-800">Randomize Questions</span>
                      {test.randomizeQuestions ? (
                        <CheckCircleIcon className="h-5 w-5 text-green-600" />
                      ) : (
                        <XCircleIcon className="h-5 w-5 text-red-600" />
                      )}
                    </div>
                    
                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <span className="font-medium text-gray-800">Show Results</span>
                      {test.showResults ? (
                        <CheckCircleIcon className="h-5 w-5 text-green-600" />
                      ) : (
                        <XCircleIcon className="h-5 w-5 text-red-600" />
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Participants and Jobs */}
              <div className="space-y-6">
                {/* Participant Statistics */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-800 mb-4">Participants</h3>
                  <div className="grid grid-cols-2 gap-3">
                    <div className="bg-blue-50 p-4 rounded-lg text-center">
                      <div className="text-2xl font-bold text-blue-600">{participantCount}</div>
                      <div className="text-sm text-gray-600">Total Assigned</div>
                    </div>
                    
                    <div className="bg-green-50 p-4 rounded-lg text-center">
                      <div className="text-2xl font-bold text-green-600">{completedCount}</div>
                      <div className="text-sm text-gray-600">Completed</div>
                    </div>
                    
                    <div className="bg-yellow-50 p-4 rounded-lg text-center">
                      <div className="text-2xl font-bold text-yellow-600">{inProgressCount}</div>
                      <div className="text-sm text-gray-600">In Progress</div>
                    </div>
                    
                    <div className="bg-gray-50 p-4 rounded-lg text-center">
                      <div className="text-2xl font-bold text-gray-600">{assignedCount}</div>
                      <div className="text-sm text-gray-600">Not Started</div>
                    </div>
                  </div>
                </div>

                {/* Associated Jobs */}
                {test.associatedJobs && test.associatedJobs.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">Associated Jobs</h3>
                    <div className="space-y-2">
                      {test.associatedJobs.map((jobId, index) => (
                        <div key={jobId} className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                          <BriefcaseIcon className="h-5 w-5 text-gray-600" />
                          <span className="text-gray-800">Job ID: {jobId}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Instructions */}
                {test.instructions && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">Instructions</h3>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <p className="text-gray-700 whitespace-pre-wrap">{test.instructions}</p>
                    </div>
                  </div>
                )}

                {/* Questions Preview */}
                {test.questions && test.questions.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">Questions Overview</h3>
                    <div className="space-y-2 max-h-40 overflow-y-auto">
                      {test.questions.map((question, index) => (
                        <div key={question._id || index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <span className="text-gray-800">Question {index + 1}</span>
                          <span className="text-blue-600 font-semibold">{question.points || 1} pts</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="bg-gray-50 px-6 py-4 flex items-center justify-end border-t border-gray-200">
            <button
              onClick={onClose}
              className="px-6 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors font-medium"
            >
              Close
            </button>
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  );
};

export default TestDetailsModal;
