import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Eye, EyeOff, Mail, Lock, LogIn } from 'lucide-react';
import useAuthStore from '../store/authStore';

const LoginPage = () => {
    const navigate = useNavigate();
    const { login, loading, error } = useAuthStore();
    const [formData, setFormData] = useState({ email: '', password: '' });
    const [showPassword, setShowPassword] = useState(false);
    const [focusedField, setFocusedField] = useState('');
    const [message, setMessage] = useState('');

    const handleChange = (e) => {
        setFormData((prev) => ({ ...prev, [e.target.name]: e.target.value }));
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        const data = await login(formData);
        if (data?.success) {
            setMessage(data.message);
            navigate('/dashboard'); // 👈 Adjust redirect path
        }
    };

    return (
        <div className="min-h-screen bg-[#f7f8fa] flex items-center justify-center px-4 py-12">
            <div className="w-full max-w-md bg-white p-8 rounded-2xl shadow-xl">
                <div className="text-center mb-8">
                    <div className="inline-flex items-center justify-center w-16 h-16 bg-[#29354d] rounded-full mb-4 shadow-lg">
                        <LogIn className="w-8 h-8 text-white" />
                    </div>
                    <h1 className="text-3xl font-bold text-[#29354d] mb-2">Welcome Back</h1>
                    <p className="text-sm text-gray-500">Sign in to your account</p>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Email */}
                    <div>
                        <label className="block text-sm font-semibold text-gray-700 mb-1">Email Address</label>
                        <div className="relative">
                            <Mail className={`absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 ${focusedField === 'email' ? 'text-[#29354d]' : 'text-gray-400'}`} />
                            <input
                                type="email"
                                name="email"
                                value={formData.email}
                                onChange={handleChange}
                                onFocus={() => setFocusedField('email')}
                                onBlur={() => setFocusedField('')}
                                required
                                placeholder="Enter your email"
                                className="w-full border border-gray-300 bg-white pl-12 pr-4 py-2 rounded-lg text-gray-800 focus:outline-none focus:ring-2 focus:ring-[#29354d]"
                            />
                        </div>
                    </div>

                    {/* Password */}
                    <div>
                        <label className="block text-sm font-semibold text-gray-700 mb-1">Password</label>
                        <div className="relative">
                            <Lock className={`absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 ${focusedField === 'password' ? 'text-[#29354d]' : 'text-gray-400'}`} />
                            <input
                                type={showPassword ? 'text' : 'password'}
                                name="password"
                                value={formData.password}
                                onChange={handleChange}
                                onFocus={() => setFocusedField('password')}
                                onBlur={() => setFocusedField('')}
                                required
                                placeholder="Enter your password"
                                className="w-full border border-gray-300 bg-white pl-12 pr-12 py-2 rounded-lg text-gray-800 focus:outline-none focus:ring-2 focus:ring-[#29354d]"
                            />
                            <button
                                type="button"
                                onClick={() => setShowPassword(!showPassword)}
                                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-[#29354d]"
                            >
                                {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                            </button>
                        </div>
                    </div>

                    {/* Forgot Password */}
                    <div className="text-right">
                        <a href="/forgot-password" className="text-sm text-[#29354d] hover:underline">Forgot Password?</a>
                    </div>

                    {/* Submit */}
                    <button
                        type="submit"
                        disabled={loading}
                        className="w-full bg-[#29354d] text-white font-semibold py-2 px-4 rounded-lg hover:bg-[#1e263a] transition"
                    >
                        {loading ? 'Signing In...' : 'Sign In'}
                    </button>
                </form>

                {/* Messages */}
                {message && <p className="mt-4 text-green-600 text-center text-sm">{message}</p>}
                {error && <p className="mt-4 text-red-600 text-center text-sm">{error}</p>}

                {/* Register Link */}
                <div className="mt-6 text-center text-sm text-gray-600">
                    Don't have an account?{' '}
                    <a href="/register" className="text-[#29354d] font-semibold hover:underline">Register</a>
                </div>
            </div>
        </div>
    );
};

export default LoginPage;
