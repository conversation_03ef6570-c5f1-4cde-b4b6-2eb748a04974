export const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

export const AUTH_ENDPOINTS = {
    LOGIN: `${API_BASE_URL}/auth/login`,
    REGISTER: `${API_BASE_URL}/auth/register`,
    LOGOUT: `${API_BASE_URL}/auth/logout`,
    CURRENT_USER: `${API_BASE_URL}/auth/me`,
    UPDATE_USER: `${API_BASE_URL}/auth/update`,
    VERIFY_OTP: `${API_BASE_URL}/auth/verify-otp`,
    RESEND_OTP: `${API_BASE_URL}/auth/resend-otp`,
};

export const COMPANY_ENDPOINTS = {
    PROFILE: `${API_BASE_URL}/company/profile`,
    JOBS: `${API_BASE_URL}/company/jobs`,
    DASHBOARD: `${API_BASE_URL}/company/dashboard`,
    JOB_APPLICATIONS: (jobId) => `${API_BASE_URL}/company/jobs/${jobId}/applications`,
    JOB_STATUS: (jobId) => `${API_BASE_URL}/company/jobs/${jobId}/status`,
};

export const QUESTION_ENDPOINTS = {
    CREATE: `${API_BASE_URL}/questions`,
    GET_ALL: `${API_BASE_URL}/questions`,
    BY_ID: (id) => `${API_BASE_URL}/questions/${id}`,
    BY_CATEGORY: `${API_BASE_URL}/questions/by-category`,
    CATEGORIES: `${API_BASE_URL}/questions/categories`,
    FILTER: `${API_BASE_URL}/questions/filter`,
};

export const TEST_ENDPOINTS = {
    CREATE: `${API_BASE_URL}/tests`,
    GET_ALL: `${API_BASE_URL}/tests`,
    BY_ID: (id) => `${API_BASE_URL}/tests/${id}`,
    UPDATE: (id) => `${API_BASE_URL}/tests/${id}`,
    DELETE: (id) => `${API_BASE_URL}/tests/${id}`,
    ASSIGN: (id) => `${API_BASE_URL}/tests/${id}/assign`,
    RESULTS: (id) => `${API_BASE_URL}/tests/${id}/results`,
    ANALYTICS: (id) => `${API_BASE_URL}/tests/${id}/analytics`,
    PARTICIPANT_FEEDBACK: (testId, participantId) =>
        `${API_BASE_URL}/tests/${testId}/participants/${participantId}/feedback`,
    ADD_QUESTIONS: (id) => `${API_BASE_URL}/tests/${id}/questions`,
    REMOVE_QUESTIONS: (id) => `${API_BASE_URL}/tests/${id}/questions`,
    ASSIGN_CANDIDATES: (id) => `${API_BASE_URL}/tests/${id}/assign-candidates`,
    // New endpoints for question management
    QUESTIONS_BY_CATEGORY: `${API_BASE_URL}/tests/questions/by-category`,
    QUESTION_CATEGORIES: `${API_BASE_URL}/tests/questions/categories`,
    FILTER_QUESTIONS: `${API_BASE_URL}/tests/questions/filter`,
    // Question bundles under tests
    QUESTION_BUNDLES: `${API_BASE_URL}/tests/question-bundles`,
    QUESTION_BUNDLE_BY_ID: (id) => `${API_BASE_URL}/tests/question-bundles/${id}`,
    // Candidates under tests
    CANDIDATES: `${API_BASE_URL}/tests/candidates`,
    CANDIDATES_SEARCH: `${API_BASE_URL}/tests/candidates/search`,
    CANDIDATES_AVAILABLE_FOR_TEST: `${API_BASE_URL}/tests/candidates/available-for-test`,
};

export const CANDIDATE_ENDPOINTS = {
    GET_ALL: `${API_BASE_URL}/candidates`,
    SEARCH: `${API_BASE_URL}/candidates/search`,
    BY_JOB: (jobId) => `${API_BASE_URL}/candidates/job/${jobId}`,
    AVAILABLE_FOR_TEST: `${API_BASE_URL}/candidates/available-for-test`,
};

export const QUESTION_BUNDLE_ENDPOINTS = {
    CREATE: `${API_BASE_URL}/tests/question-bundles`,
    GET_ALL: `${API_BASE_URL}/tests/question-bundles`,
    BY_ID: (id) => `${API_BASE_URL}/tests/question-bundles/${id}`,
    UPDATE: (id) => `${API_BASE_URL}/tests/question-bundles/${id}`,
    DELETE: (id) => `${API_BASE_URL}/tests/question-bundles/${id}`,
};

