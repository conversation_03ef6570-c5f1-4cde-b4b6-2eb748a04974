import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import toast from 'react-hot-toast';

// Components
import AllQuestions from './components/AllQuestions';
import QuestionUpload from './components/QuestionUpload';
import ManualQuestionForm from './components/ManualQuestionForm';
import TestInterface from './components/TestInterface';

// Hooks
import useQuestionManager from './hooks/useQuestionManager';

const Aptitude = () => {
  const [currentView, setCurrentView] = useState('upload'); // 'upload', 'questions', 'test'
  const [showManualModal, setShowManualModal] = useState(false);
  const [editingQuestion, setEditingQuestion] = useState(null);

  const navigate = useNavigate();

  // Use the question manager hook
  const {
    questions,
    loading,
    error,
    addQuestion,
    updateQuestion,
    deleteQuestion,
    deleteQuestions,
    uploadQuestionsFromFile,
    exportQuestions,
    setError
  } = useQuestionManager();

  // Get total questions count
  const getQuestionsCount = () => {
    return questions.length;
  };

  const handleEditQuestion = (question) => {
    setEditingQuestion(question);
    setShowManualModal(true);
  };

  const handleDeleteQuestion = async (questionId) => {
    if (window.confirm('Are you sure you want to delete this question?')) {
      const result = await deleteQuestion(questionId);
      if (result.success) {
        toast.success('Question deleted successfully');
      } else {
        toast.error('Failed to delete question');
      }
    }
  };

  const handleDeleteMultiple = async (questionIds) => {
    if (window.confirm(`Are you sure you want to delete ${questionIds.length} questions?`)) {
      const result = await deleteQuestions(questionIds);
      if (result.success) {
        toast.success(`Deleted ${questionIds.length} questions`);
      } else {
        toast.error('Failed to delete questions');
      }
    }
  };

  const handleExport = () => {
    const result = exportQuestions();
    if (result.success) {
      toast.success('Questions exported successfully');
    } else {
      toast.error(result.error || 'Failed to export questions');
    }
  };

  const handleTestComplete = (results) => {
    console.log('Test completed with results:', results);
    toast.success(`Test completed! Score: ${results.score}%`);
  };

  const handleCloseModal = () => {
    setShowManualModal(false);
    setEditingQuestion(null);
  };

  // Handle file upload
  const handleFileUpload = async (file) => {
    try {
      const result = await uploadQuestionsFromFile(file);
      return result;
    } catch (error) {
      toast.error('An error occurred while uploading the file');
      return { success: false, error: error.message };
    }
  };

  // Handle manual question submission
  const handleManualSubmit = async (questionData) => {
    try {
      if (editingQuestion) {
        const result = await updateQuestion(editingQuestion._id || editingQuestion.id, questionData);
        if (result.success) {
          setEditingQuestion(null);
          return result;
        }
        return result;
      } else {
        const result = await addQuestion(questionData);
        return result;
      }
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  // Navigation functions
  const handleViewChange = (view) => {
    setCurrentView(view);
  };

  const handleAddQuestionClick = () => {
    setEditingQuestion(null);
    setShowManualModal(true);
  };

  // Render different views based on current state
  const renderMainContent = () => {
    switch (currentView) {
      case 'questions':
        return (
          <AllQuestions
            questions={questions}
            selectedJob={null}
            onEditQuestion={handleEditQuestion}
            onDeleteQuestion={handleDeleteQuestion}
            onDeleteMultiple={handleDeleteMultiple}
            onExport={handleExport}
            onAddQuestion={handleAddQuestionClick}
          />
        );
      case 'test':
        return (
          <TestInterface
            questions={questions}
            selectedJob={null}
            onTestComplete={handleTestComplete}
            timePerQuestion={60}
            showResults={true}
          />
        );
      default:
        return (
          <div className="w-full max-w-4xl mx-auto">
            {/* Header */}
            <motion.div
              className="bg-white rounded-xl shadow-lg border border-gray-200 p-8 w-full flex items-center justify-center mb-8"
              initial={{ opacity: 0, y: 40, scale: 1.1 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              transition={{ duration: 1.2, type: 'spring', stiffness: 60 }}
            >
              <h2 className="text-3xl font-bold text-gray-800 text-center">Question Management</h2>
            </motion.div>

            {/* Upload/Manual Question Section */}
            <motion.div
              className="flex flex-col items-center justify-center min-h-[60vh] w-full"
              initial="hidden"
              animate="visible"
              variants={{}}
            >
              <motion.div
                className="flex flex-col md:flex-row gap-8 w-full max-w-4xl justify-center"
                initial="hidden"
                animate="visible"
                variants={{ visible: { transition: { staggerChildren: 0.5 } } }}
              >
                {/* Upload Card */}
                <QuestionUpload
                  onUpload={handleFileUpload}
                  loading={loading}
                  className="flex-1"
                />

                {/* OR Divider */}
                <div className="hidden md:flex flex-col justify-center items-center">
                  <span className="text-gray-400 font-bold text-lg">or</span>
                </div>

                {/* Manual Add Card */}
                <motion.div
                  className="flex-1 bg-white rounded-2xl shadow-xl p-8 flex flex-col items-center min-w-[280px] min-h-[340px] justify-center"
                  initial={{ opacity: 0, y: 40, scale: 1.1 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  transition={{ duration: 1.7, type: 'spring', stiffness: 60, delay: 0.5 }}
                >
                  <button
                    className="px-8 py-4 bg-[rgb(35,65,75)] text-white rounded-lg font-bold text-lg shadow hover:bg-[rgb(45,85,100)] transition-all"
                    onClick={handleAddQuestionClick}
                  >
                    Add Question Manually
                  </button>
                </motion.div>
              </motion.div>
            </motion.div>

            {/* Action Buttons */}
            {getQuestionsCount() > 0 && (
              <motion.div
                className="flex justify-center gap-4 mt-8"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.8 }}
              >
                <button
                  onClick={() => handleViewChange('questions')}
                  className="px-6 py-3 bg-blue-600 text-white rounded-xl font-bold shadow hover:bg-blue-700 transition-all"
                >
                  View All Questions ({getQuestionsCount()})
                </button>
                <button
                  onClick={() => handleViewChange('test')}
                  className="px-6 py-3 bg-green-600 text-white rounded-xl font-bold shadow hover:bg-green-700 transition-all"
                >
                  Start Test
                </button>
              </motion.div>
            )}
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen w-full bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-800">Question Management</h1>
          <button
            onClick={() => navigate(-1)}
            className="flex items-center gap-2 px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" d="M7 12l10 0" />
            </svg>
            Back
          </button>
        </div>
      </div>

      {/* Main Content */}
      <main className="flex flex-col items-center justify-start py-16 px-4 md:px-10 lg:px-24">
        {renderMainContent()}
      </main>

      {/* Manual Question Form Modal */}
      <ManualQuestionForm
        isOpen={showManualModal}
        onClose={handleCloseModal}
        onSubmit={handleManualSubmit}
        editingQuestion={editingQuestion}
      />

      {/* Error Display */}
      {error && (
        <div className="fixed bottom-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded z-50">
          <span className="block sm:inline">{error}</span>
          <button
            onClick={() => setError(null)}
            className="ml-2 text-red-700 hover:text-red-900"
          >
            ×
          </button>
        </div>
      )}
    </div>
  );
};


export default Aptitude;
