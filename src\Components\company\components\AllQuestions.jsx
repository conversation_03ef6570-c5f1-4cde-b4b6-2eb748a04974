import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  MagnifyingGlassIcon,
  FunnelIcon,
  TrashIcon,
  DocumentArrowDownIcon,
  PlusIcon,
  EyeIcon,
  EyeSlashIcon,
  CheckIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import QuestionCard from './QuestionCard';
import QuestionModal from './QuestionModal';
import toast from 'react-hot-toast';

const AllQuestions = ({ 
  questions, 
  selectedJob, 
  onEditQuestion, 
  onDeleteQuestion, 
  onDeleteMultiple,
  onExport,
  onAddQuestion 
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [showAnswers, setShowAnswers] = useState(false);
  const [selectedQuestions, setSelectedQuestions] = useState(new Set());
  const [isSelectionMode, setIsSelectionMode] = useState(false);
  const [sortBy, setSortBy] = useState('newest');
  const [filterBy, setFilterBy] = useState('all');
  const [selectedQuestionForModal, setSelectedQuestionForModal] = useState(null);

  // Get questions for selected job (now using category matching)
  const jobQuestions = useMemo(() => {
    if (!selectedJob) return questions || [];
    // Since we removed job association, show all questions
    // You might want to filter by category that relates to the job
    return questions || [];
  }, [questions, selectedJob]);

  // Filter and search questions
  const filteredQuestions = useMemo(() => {
    let filtered = [...jobQuestions];

    // Apply search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(q => {
        const questionText = q.questionText || q.question || '';

        // Search in question text
        if (questionText.toLowerCase().includes(term)) return true;

        // Search in options for MCQ/Multiple-Select
        if (q.options && Array.isArray(q.options)) {
          if (q.options.some(opt => opt.text?.toLowerCase().includes(term))) return true;
        }

        // Search in correct answer
        if ((q.correctAnswer || q.answer)?.toLowerCase().includes(term)) return true;

        // Search in explanation
        if (q.explanation?.toLowerCase().includes(term)) return true;

        // Search in category
        if (q.category?.toLowerCase().includes(term)) return true;

        return false;
      });
    }

    // Apply filter (now by question type)
    if (filterBy !== 'all') {
      filtered = filtered.filter(q => q.questionType === filterBy);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'newest':
          return new Date(b.createdAt || 0) - new Date(a.createdAt || 0);
        case 'oldest':
          return new Date(a.createdAt || 0) - new Date(b.createdAt || 0);
        case 'question':
          const aText = a.questionText || a.question || '';
          const bText = b.questionText || b.question || '';
          return aText.localeCompare(bText);
        default:
          return 0;
      }
    });

    return filtered;
  }, [jobQuestions, searchTerm, filterBy, sortBy]);

  const handleSelectQuestion = (questionId) => {
    if (!isSelectionMode) return;
    
    const newSelected = new Set(selectedQuestions);
    if (newSelected.has(questionId)) {
      newSelected.delete(questionId);
    } else {
      newSelected.add(questionId);
    }
    setSelectedQuestions(newSelected);
  };

  const handleSelectAll = () => {
    if (selectedQuestions.size === filteredQuestions.length) {
      setSelectedQuestions(new Set());
    } else {
      setSelectedQuestions(new Set(filteredQuestions.map(q => q._id || q.id)));
    }
  };

  const handleDeleteSelected = () => {
    if (selectedQuestions.size === 0) return;
    
    const questionIds = Array.from(selectedQuestions);
    onDeleteMultiple?.(questionIds);
    setSelectedQuestions(new Set());
    setIsSelectionMode(false);
    toast.success(`Deleted ${questionIds.length} question(s)`);
  };

  const handleExport = () => {
    onExport?.();
    toast.success('Questions exported successfully');
  };

  const toggleSelectionMode = () => {
    setIsSelectionMode(!isSelectionMode);
    setSelectedQuestions(new Set());
  };

  const handleViewQuestion = (question) => {
    setSelectedQuestionForModal(question);
  };

  const closeQuestionModal = () => {
    setSelectedQuestionForModal(null);
  };

  return (
    <div className="w-full max-w-6xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-3xl font-bold text-gray-800">
              All Questions
            </h2>
            <p className="text-gray-600 mt-1">
              {filteredQuestions.length} of {jobQuestions.length} questions
            </p>
          </div>
          <button
            onClick={onAddQuestion}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <PlusIcon className="h-4 w-4" />
            Add Question
          </button>
        </div>

        {/* Controls */}
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          {/* Search */}
          <div className="relative flex-1 max-w-md">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search questions..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Filters and Actions */}
          <div className="flex items-center gap-2">
            {/* Sort */}
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            >
              <option value="newest">Newest First</option>
              <option value="oldest">Oldest First</option>
              <option value="question">By Question</option>
            </select>

            {/* Filter */}
            <select
              value={filterBy}
              onChange={(e) => setFilterBy(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Types</option>
              <option value="MCQ">MCQ</option>
              <option value="Multiple-Select">Multiple-Select</option>
              <option value="Short-Answer">Short-Answer</option>
              <option value="Code">Code</option>
            </select>

            {/* Show Answers Toggle */}
            <button
              onClick={() => setShowAnswers(!showAnswers)}
              className={`p-2 rounded-lg transition-colors ${
                showAnswers 
                  ? 'bg-green-100 text-green-600' 
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
              title={showAnswers ? 'Hide Answers' : 'Show Answers'}
            >
              {showAnswers ? <EyeSlashIcon className="h-4 w-4" /> : <EyeIcon className="h-4 w-4" />}
            </button>

            {/* Selection Mode Toggle */}
            <button
              onClick={toggleSelectionMode}
              className={`p-2 rounded-lg transition-colors ${
                isSelectionMode 
                  ? 'bg-blue-100 text-blue-600' 
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
              title="Selection Mode"
            >
              <CheckIcon className="h-4 w-4" />
            </button>

            {/* Export */}
            <button
              onClick={handleExport}
              className="p-2 bg-gray-100 text-gray-600 hover:bg-gray-200 rounded-lg transition-colors"
              title="Export Questions"
            >
              <DocumentArrowDownIcon className="h-4 w-4" />
            </button>
          </div>
        </div>

        {/* Selection Mode Controls */}
        <AnimatePresence>
          {isSelectionMode && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <button
                    onClick={handleSelectAll}
                    className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                  >
                    {selectedQuestions.size === filteredQuestions.length ? 'Deselect All' : 'Select All'}
                  </button>
                  <span className="text-sm text-gray-600">
                    {selectedQuestions.size} selected
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <button
                    onClick={handleDeleteSelected}
                    disabled={selectedQuestions.size === 0}
                    className="flex items-center gap-1 px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <TrashIcon className="h-3 w-3" />
                    Delete Selected
                  </button>
                  <button
                    onClick={toggleSelectionMode}
                    className="p-1 text-gray-500 hover:text-gray-700"
                  >
                    <XMarkIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Questions Grid */}
      {filteredQuestions.length === 0 ? (
        <div className="flex flex-col items-center justify-center h-64 text-gray-500">
          <div className="text-6xl mb-4">🤔</div>
          <h3 className="text-xl font-semibold mb-2">No Questions Found</h3>
          <p className="text-center">
            {searchTerm 
              ? 'Try adjusting your search terms or filters.'
              : 'Start by adding some questions for this job.'
            }
          </p>
        </div>
      ) : (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {filteredQuestions.map((question) => (
            <QuestionCard
              key={question._id || question.id}
              question={question}
              onEdit={onEditQuestion}
              onDelete={onDeleteQuestion}
              onView={handleViewQuestion}
              showAnswer={showAnswers}
              isSelected={selectedQuestions.has(question._id || question.id)}
              onSelect={isSelectionMode ? handleSelectQuestion : null}
              showActions={!isSelectionMode}
            />
          ))}
        </div>
      )}

      {/* Question Detail Modal */}
      <QuestionModal
        isOpen={!!selectedQuestionForModal}
        onClose={closeQuestionModal}
        question={selectedQuestionForModal}
        onEdit={onEditQuestion}
        onDelete={onDeleteQuestion}
      />
    </div>
  );
};

export default AllQuestions;
