import { useState, useEffect, useCallback } from 'react';
import * as XLSX from 'xlsx';
import useCompanyStore from '../../../store/companyStore';

const useQuestionManager = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  // Get store methods and state
  const {
    questions,
    createQuestion,
    getQuestions,
    updateQuestion,
    deleteQuestion: deleteQuestionAPI,
    error: storeError,
    clearError
  } = useCompanyStore();

  // Load questions on mount
  useEffect(() => {
    const loadQuestions = async () => {
      setLoading(true);
      try {
        await getQuestions();
      } catch (err) {
        setError('Failed to load questions');
      } finally {
        setLoading(false);
      }
    };
    
    loadQuestions();
  }, [getQuestions]);

  // Sync store error with local error
  useEffect(() => {
    if (storeError) {
      setError(storeError);
    }
  }, [storeError]);

  // Add a single question
  const addQuestion = useCallback(async (questionData) => {
    try {
      setLoading(true);
      setError(null);

      // Send data directly as it matches the backend schema
      const result = await createQuestion(questionData);

      if (result) {
        // Refresh questions list
        await getQuestions();
        return { success: true, question: result };
      } else {
        return { success: false, error: 'Failed to create question' };
      }
    } catch (err) {
      setError('Failed to add question');
      return { success: false, error: err.message };
    } finally {
      setLoading(false);
    }
  }, [createQuestion, getQuestions]);

  // Update a question
  const updateQuestionData = useCallback(async (questionId, updates) => {
    try {
      setLoading(true);
      setError(null);

      // Send updates directly as they match the backend schema
      const result = await updateQuestion(questionId, updates);

      if (result) {
        // Refresh questions list
        await getQuestions();
        return { success: true };
      } else {
        return { success: false, error: 'Failed to update question' };
      }
    } catch (err) {
      setError('Failed to update question');
      return { success: false, error: err.message };
    } finally {
      setLoading(false);
    }
  }, [updateQuestion, getQuestions]);

  // Delete a question
  const deleteQuestion = useCallback(async (questionId) => {
    try {
      setLoading(true);
      setError(null);
      
      await deleteQuestionAPI(questionId);
      // Refresh questions list
      await getQuestions();
      return { success: true };
    } catch (err) {
      setError('Failed to delete question');
      return { success: false, error: err.message };
    } finally {
      setLoading(false);
    }
  }, [deleteQuestionAPI, getQuestions]);

  // Delete multiple questions
  const deleteQuestions = useCallback(async (questionIds) => {
    try {
      setLoading(true);
      setError(null);
      
      // Delete questions one by one
      for (const id of questionIds) {
        await deleteQuestionAPI(id);
      }
      
      // Refresh questions list
      await getQuestions();
      return { success: true };
    } catch (err) {
      setError('Failed to delete questions');
      return { success: false, error: err.message };
    } finally {
      setLoading(false);
    }
  }, [deleteQuestionAPI, getQuestions]);

  // Upload questions from Excel file
  const uploadQuestionsFromFile = useCallback(async (file) => {
    return new Promise((resolve) => {
      setLoading(true);
      setError(null);
      
      const reader = new FileReader();
      reader.onload = async (evt) => {
        try {
          const data = new Uint8Array(evt.target.result);
          const workbook = XLSX.read(data, { type: 'array' });
          const sheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[sheetName];
          const jsonData = XLSX.utils.sheet_to_json(worksheet);
          
          let successCount = 0;
          let errorCount = 0;
          
          // Process each question
          for (const row of jsonData) {
            if (row.questionText || row.question) {
              try {
                // Map Excel columns to schema format
                const questionData = {
                  questionText: row.questionText || row.question,
                  questionType: row.questionType || 'MCQ',
                  category: row.category || 'Aptitude',
                  difficulty: row.difficulty || 'Medium',
                  points: parseInt(row.points) || 1
                };

                // Handle options for MCQ/Multiple-Select
                if (questionData.questionType === 'MCQ' || questionData.questionType === 'Multiple-Select') {
                  const options = [];
                  for (let i = 1; i <= 4; i++) {
                    const optionText = row[`option${i}`];
                    if (optionText) {
                      options.push({
                        text: optionText,
                        isCorrect: optionText === (row.correctAnswer || row.answer)
                      });
                    }
                  }
                  if (options.length >= 2) {
                    questionData.options = options;
                  }
                } else {
                  // For Short-Answer/Code questions
                  questionData.correctAnswer = row.correctAnswer || row.answer || '';
                }

                // Add explanation if provided
                if (row.explanation) {
                  questionData.explanation = row.explanation;
                }

                await createQuestion(questionData);
                successCount++;
              } catch (err) {
                errorCount++;
                console.error('Error creating question:', err);
              }
            }
          }
          
          // Refresh questions list
          await getQuestions();
          
          setLoading(false);
          resolve({ 
            success: true, 
            count: successCount,
            errors: errorCount,
            message: errorCount > 0 ? `${successCount} questions uploaded, ${errorCount} failed` : `${successCount} questions uploaded successfully`
          });
        } catch (err) {
          setError('Failed to parse Excel file');
          setLoading(false);
          resolve({ success: false, error: err.message });
        }
      };
      
      reader.onerror = () => {
        setError('Failed to read file');
        setLoading(false);
        resolve({ success: false, error: 'File read error' });
      };
      
      reader.readAsArrayBuffer(file);
    });
  }, [createQuestion, getQuestions]);

  // Get questions for a specific job (now filtering by category)
  const getQuestionsForJob = useCallback((jobTitle) => {
    if (!jobTitle || !questions) return [];
    // Since we removed job association, return questions by category
    // You might want to filter by category that matches the job type
    return questions.filter(q =>
      q.category && q.category.toLowerCase().includes(jobTitle.toLowerCase()) ||
      q.questionText && q.questionText.toLowerCase().includes(jobTitle.toLowerCase())
    );
  }, [questions]);

  // Search questions
  const searchQuestions = useCallback((searchTerm, category = null) => {
    let filteredQuestions = category
      ? questions.filter(q => q.category === category)
      : questions || [];

    if (!searchTerm) return filteredQuestions;

    const term = searchTerm.toLowerCase();
    return filteredQuestions.filter(q => {
      // Search in question text
      if (q.questionText?.toLowerCase().includes(term)) return true;

      // Search in options for MCQ/Multiple-Select
      if (q.options && Array.isArray(q.options)) {
        return q.options.some(opt => opt.text?.toLowerCase().includes(term));
      }

      // Search in correct answer
      if (q.correctAnswer?.toLowerCase().includes(term)) return true;

      // Search in explanation
      if (q.explanation?.toLowerCase().includes(term)) return true;

      // Search in category
      if (q.category?.toLowerCase().includes(term)) return true;

      return false;
    });
  }, [questions]);

  // Export questions to Excel
  const exportQuestions = useCallback((category = null) => {
    try {
      const questionsToExport = category
        ? questions.filter(q => q.category === category)
        : questions || [];

      if (questionsToExport.length === 0) {
        return { success: false, error: 'No questions to export' };
      }

      const exportData = questionsToExport.map(q => {
        const baseData = {
          questionText: q.questionText,
          questionType: q.questionType,
          category: q.category,
          difficulty: q.difficulty,
          points: q.points,
          explanation: q.explanation || ''
        };

        // Add options for MCQ/Multiple-Select
        if (q.options && Array.isArray(q.options)) {
          q.options.forEach((option, index) => {
            baseData[`option${index + 1}`] = option.text;
            if (option.isCorrect) {
              baseData.correctAnswer = option.text;
            }
          });
        } else if (q.correctAnswer) {
          baseData.correctAnswer = q.correctAnswer;
        }

        return baseData;
      });

      const worksheet = XLSX.utils.json_to_sheet(exportData);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Questions');

      const fileName = category
        ? `${category}_questions.xlsx`
        : 'all_questions.xlsx';

      XLSX.writeFile(workbook, fileName);
      return { success: true };
    } catch (err) {
      return { success: false, error: 'Failed to export questions' };
    }
  }, [questions]);

  // Clear error
  const clearErrorState = useCallback(() => {
    setError(null);
    clearError();
  }, [clearError]);

  return {
    questions: questions || [],
    loading,
    error,
    addQuestion,
    updateQuestion: updateQuestionData,
    deleteQuestion,
    deleteQuestions,
    uploadQuestionsFromFile,
    getQuestionsForJob,
    searchQuestions,
    exportQuestions,
    setError: clearErrorState
  };
};

export default useQuestionManager;
