import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  XMarkIcon,
  ChartBarIcon,
  UserIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline';
import useCompanyStore from '../../../store/companyStore';

const TestResultsModal = ({ isOpen, onClose, test }) => {
  const { getTestResults, getTestAnalytics } = useCompanyStore();
  const [results, setResults] = useState([]);
  const [analytics, setAnalytics] = useState(null);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('results'); // 'results' or 'analytics'

  useEffect(() => {
    if (isOpen && test) {
      fetchTestData();
    }
  }, [isOpen, test]);

  const fetchTestData = async () => {
    setLoading(true);
    try {
      const [resultsData, analyticsData] = await Promise.all([
        getTestResults(test._id),
        getTestAnalytics(test._id)
      ]);
      
      setResults(resultsData || []);
      setAnalytics(analyticsData || null);
    } catch (error) {
      console.error('Error fetching test data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen || !test) return null;

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getScoreColor = (score, passingScore) => {
    if (score >= passingScore) return 'text-green-600';
    if (score >= passingScore * 0.7) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="h-5 w-5 text-green-600" />;
      case 'in-progress':
        return <ClockIcon className="h-5 w-5 text-yellow-600" />;
      case 'assigned':
        return <ExclamationTriangleIcon className="h-5 w-5 text-gray-600" />;
      default:
        return <XCircleIcon className="h-5 w-5 text-red-600" />;
    }
  };

  const renderResults = () => {
    if (loading) {
      return (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-2 border-blue-600 border-t-transparent"></div>
          <span className="ml-3 text-gray-600">Loading results...</span>
        </div>
      );
    }

    if (results.length === 0) {
      return (
        <div className="text-center py-12">
          <UserIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-600 mb-2">No Results Yet</h3>
          <p className="text-gray-500">No candidates have completed this test yet.</p>
        </div>
      );
    }

    return (
      <div className="space-y-4">
        {/* Results Header */}
        <div className="grid grid-cols-6 gap-4 p-3 bg-gray-50 rounded-lg font-medium text-gray-700 text-sm">
          <div>Candidate</div>
          <div>Status</div>
          <div>Score</div>
          <div>Time Taken</div>
          <div>Completed</div>
          <div>Actions</div>
        </div>

        {/* Results List */}
        <div className="space-y-2 max-h-96 overflow-y-auto">
          {results.map((result, index) => (
            <motion.div
              key={result.candidateId || index}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.05 }}
              className="grid grid-cols-6 gap-4 p-4 bg-white border border-gray-200 rounded-lg hover:shadow-md transition-shadow"
            >
              <div className="flex items-center gap-2">
                <UserIcon className="h-5 w-5 text-gray-400" />
                <span className="font-medium text-gray-800">
                  {result.candidateName || `Candidate ${index + 1}`}
                </span>
              </div>

              <div className="flex items-center gap-2">
                {getStatusIcon(result.status)}
                <span className="capitalize text-sm">{result.status}</span>
              </div>

              <div className="flex items-center">
                {result.score !== undefined ? (
                  <span className={`font-bold ${getScoreColor(result.score, test.passingScore)}`}>
                    {result.score}%
                  </span>
                ) : (
                  <span className="text-gray-400">-</span>
                )}
              </div>

              <div className="flex items-center gap-1">
                <ClockIcon className="h-4 w-4 text-gray-400" />
                <span className="text-sm text-gray-600">
                  {result.timeTaken ? `${result.timeTaken} min` : '-'}
                </span>
              </div>

              <div className="text-sm text-gray-600">
                {result.completedAt ? formatDate(result.completedAt) : '-'}
              </div>

              <div className="flex items-center gap-2">
                {result.status === 'completed' && (
                  <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">
                    View Details
                  </button>
                )}
                {result.suspiciousActivity && (
                  <ExclamationTriangleIcon className="h-4 w-4 text-red-500" title="Suspicious Activity Detected" />
                )}
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    );
  };

  const renderAnalytics = () => {
    if (loading) {
      return (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-2 border-blue-600 border-t-transparent"></div>
          <span className="ml-3 text-gray-600">Loading analytics...</span>
        </div>
      );
    }

    if (!analytics) {
      return (
        <div className="text-center py-12">
          <ChartBarIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-600 mb-2">No Analytics Available</h3>
          <p className="text-gray-500">Analytics will be available once candidates complete the test.</p>
        </div>
      );
    }

    return (
      <div className="space-y-6">
        {/* Summary Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-blue-50 p-4 rounded-lg text-center">
            <div className="text-2xl font-bold text-blue-600">{analytics.totalParticipants || 0}</div>
            <div className="text-sm text-gray-600">Total Participants</div>
          </div>
          
          <div className="bg-green-50 p-4 rounded-lg text-center">
            <div className="text-2xl font-bold text-green-600">{analytics.completionRate || 0}%</div>
            <div className="text-sm text-gray-600">Completion Rate</div>
          </div>
          
          <div className="bg-yellow-50 p-4 rounded-lg text-center">
            <div className="text-2xl font-bold text-yellow-600">{analytics.averageScore || 0}%</div>
            <div className="text-sm text-gray-600">Average Score</div>
          </div>
          
          <div className="bg-purple-50 p-4 rounded-lg text-center">
            <div className="text-2xl font-bold text-purple-600">{analytics.passRate || 0}%</div>
            <div className="text-sm text-gray-600">Pass Rate</div>
          </div>
        </div>

        {/* Score Distribution */}
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <h4 className="text-lg font-semibold text-gray-800 mb-4">Score Distribution</h4>
          <div className="space-y-3">
            {analytics.scoreDistribution?.map((range, index) => (
              <div key={index} className="flex items-center gap-4">
                <div className="w-20 text-sm text-gray-600">{range.range}</div>
                <div className="flex-1 bg-gray-200 rounded-full h-4 relative">
                  <div
                    className="bg-blue-600 h-4 rounded-full transition-all duration-500"
                    style={{ width: `${range.percentage}%` }}
                  ></div>
                </div>
                <div className="w-16 text-sm text-gray-600 text-right">
                  {range.count} ({range.percentage}%)
                </div>
              </div>
            )) || (
              <div className="text-gray-500 text-center py-4">No score distribution data available</div>
            )}
          </div>
        </div>

        {/* Time Analytics */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-white p-6 rounded-lg border border-gray-200">
            <h4 className="text-lg font-semibold text-gray-800 mb-4">Time Statistics</h4>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Average Time:</span>
                <span className="font-medium">{analytics.averageTime || 0} min</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Fastest Completion:</span>
                <span className="font-medium">{analytics.fastestTime || 0} min</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Slowest Completion:</span>
                <span className="font-medium">{analytics.slowestTime || 0} min</span>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg border border-gray-200">
            <h4 className="text-lg font-semibold text-gray-800 mb-4">Question Analytics</h4>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Most Difficult:</span>
                <span className="font-medium">Q{analytics.mostDifficultQuestion || '-'}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Easiest:</span>
                <span className="font-medium">Q{analytics.easiestQuestion || '-'}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Avg. Correct Rate:</span>
                <span className="font-medium">{analytics.averageCorrectRate || 0}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <AnimatePresence>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          className="bg-white rounded-2xl shadow-2xl w-full max-w-6xl max-h-[90vh] overflow-hidden"
        >
          {/* Header */}
          <div className="bg-gradient-to-r from-green-600 to-green-700 text-white p-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold mb-2">Test Results & Analytics</h2>
                <p className="text-green-100">{test.testName}</p>
              </div>
              <button
                onClick={onClose}
                className="p-2 hover:bg-white/20 rounded-lg transition-colors"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>

            {/* Tabs */}
            <div className="flex gap-4 mt-6">
              <button
                onClick={() => setActiveTab('results')}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  activeTab === 'results'
                    ? 'bg-white text-green-700'
                    : 'text-green-100 hover:text-white hover:bg-white/20'
                }`}
              >
                Results
              </button>
              <button
                onClick={() => setActiveTab('analytics')}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  activeTab === 'analytics'
                    ? 'bg-white text-green-700'
                    : 'text-green-100 hover:text-white hover:bg-white/20'
                }`}
              >
                Analytics
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="p-6 overflow-y-auto max-h-[70vh]">
            {activeTab === 'results' ? renderResults() : renderAnalytics()}
          </div>

          {/* Footer */}
          <div className="bg-gray-50 px-6 py-4 flex items-center justify-between border-t border-gray-200">
            <div className="text-sm text-gray-600">
              Last updated: {formatDate(new Date())}
            </div>
            <button
              onClick={onClose}
              className="px-6 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors font-medium"
            >
              Close
            </button>
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  );
};

export default TestResultsModal;
